/**
 * Qax 共享配置
 * 前后端共享的 Qax 配置类型、常量和函数
 * <AUTHOR>
 */

// ClineMessage import removed - todo functions moved to QaxTodoListTool.ts

// Qax 环境类型
export type QaxEnvironment = "dev" | "test" | "prod"

// Qax Todo 状态类型
export type QaxTodoStatus = "pending" | "in_progress" | "completed"

// Qax Todo 项接口
export interface QaxTodoItem {
	id: string
	content: string
	status: QaxTodoStatus
}

// Qax 默认模型配置
export const qaxDefaultModels = ["DeepSeek-V3", "DeepSeek-R1"] as const
export const qaxDefaultModelId = "DeepSeek-V3" as const

// Qax 扩展标识符和回调配置
export const QAX_EXTENSION_ID = "qi-anxin-group.qax-codegen" as const
export const QAX_AUTH_CALLBACK_PATH = "/qax-codegen-auth" as const

/**
 * Qax 配置接口
 */
export interface QaxConfig {
	isQaxMode: boolean
	environment: QaxEnvironment
	qaxCodegenDomain: string
	qaxAIPlatformDomain: string
	enableAutocomplete: boolean
	defaultModel: string
	defaultModels: readonly string[]
}

/**
 * 域名配置映射
 */
const DOMAIN_CONFIG: Record<QaxEnvironment, string> = {
	dev: "https://codegen-dev.qianxin-inc.cn",
	test: "https://codegen-test.qianxin-inc.cn",
	prod: "https://codegen.qianxin-inc.cn",
}

/**
 * 获取 Qax 环境类型
 */
function getEnvironment(): QaxEnvironment {
	const env = process.env.QAX_ENVIRONMENT?.toLowerCase() as QaxEnvironment
	return DOMAIN_CONFIG[env] ? env : "prod"
}

/**
 * 获取Qax配置
 */
export function getQaxConfig(): QaxConfig {
	const environment = getEnvironment()

	return {
		isQaxMode: process.env.QAX_ENVIRONMENT !== "false",
		environment,
		qaxCodegenDomain: process.env.QAX_CODEGEN_DOMAIN || DOMAIN_CONFIG[environment],
		qaxAIPlatformDomain: process.env.QAX_AI_DOMAIN || "https://aip.b.qianxin-inc.cn",
		enableAutocomplete: process.env.QAX_AUTOCOMPLETE !== "false",
		defaultModel: qaxDefaultModelId,
		defaultModels: qaxDefaultModels,
	}
}

/**
 * 获取当前使用的扩展 ID
 * 开发时默认使用 Cline 的扩展 ID，打包时使用 QAX 的扩展 ID
 */
export function getCurrentExtensionId(): string {
	return process.env.VSCODE_EXTENSION_ID || "saoudrizwan.claude-dev"
}

/**
 * 获取内存配置键
 */
export function getMemoryConfigKey(): string {
	return "qax-codegen.memory"
}

/**
 * 获取 Qax Codegen 登录地址
 * Following Codegen 3.0.0 URL construction pattern
 */
export function getQaxCodegenLoginUrl(callbackUrl?: string, state?: string): string {
	const domain = getQaxConfig().qaxCodegenDomain

	// Use URL object for more graceful query construction (following Codegen 3.0.0 pattern)
	const authUrl = new URL(`${domain}/api/v1/auth/sso/login`)

	if (callbackUrl) {
		authUrl.searchParams.set("ref", callbackUrl)
	}

	if (state) {
		authUrl.searchParams.set("state", state)
	}

	return authUrl.toString()
}

/**
 * 获取 Qax Codegen Base URL (API 基础地址)
 */
export function getQaxCodegenBaseUrl(): string {
	const domain = getQaxConfig().qaxCodegenDomain
	return `${domain}/api/v1`
}

/**
 * 获取 Qax 大模型平台 Base URL (API 基础地址)
 */
export function getQaxAIPlatformBaseUrl(): string {
	const domain = getQaxConfig().qaxAIPlatformDomain
	return `${domain}/v2`
}

/**
 * 获取 Qax Codegen 模型列表接口 URL
 */
export function getQaxCodegenModelsUrl(): string {
	const baseUrl = getQaxCodegenBaseUrl()
	return `${baseUrl}/chat/models`
}

/**
 * 获取 Qax 大模型平台模型列表接口 URL
 */
export function getQaxAIPlatformModelsUrl(): string {
	const domain = getQaxConfig().qaxAIPlatformDomain
	return `${domain}/v1/chat/models`
}

// ==================== 便捷访问函数 ====================

/**
 * 获取 Qax Codegen 域名
 */
export function getQaxCodegenDomain(): string {
	return getQaxConfig().qaxCodegenDomain
}

/**
 * 获取 Qax 大模型平台域名
 */
export function getQaxAIPlatformDomain(): string {
	return getQaxConfig().qaxAIPlatformDomain
}

/**
 * 获取默认模型
 */
export function getQaxDefaultModel(): string {
	return getQaxConfig().defaultModel
}

/**
 * 获取默认模型列表
 */
export function getQaxDefaultModels(): readonly string[] {
	return getQaxConfig().defaultModels
}

/**
 * 获取当前环境
 */
export function getQaxCurrentEnvironment(): QaxEnvironment {
	return getQaxConfig().environment
}
