/**
 * Test file to verify that autocomplete logging is working correctly
 * This file can be used to manually test the logging functionality
 */

import * as vscode from "vscode"

// Mock console.log to capture logs for testing
const originalConsoleLog = console.log
const capturedLogs: string[] = []

export function startLogCapture() {
	capturedLogs.length = 0
	console.log = (...args: any[]) => {
		const logMessage = args.map((arg) => (typeof arg === "string" ? arg : JSON.stringify(arg, null, 2))).join(" ")
		capturedLogs.push(logMessage)
		originalConsoleLog(...args)
	}
}

export function stopLogCapture() {
	console.log = originalConsoleLog
	return [...capturedLogs]
}

export function getLogsContaining(searchTerm: string): string[] {
	return capturedLogs.filter((log) => log.includes(searchTerm))
}

export function verifyLoggingStructure(): {
	hasRequestLogs: boolean
	hasResponseLogs: boolean
	hasFinalProcessingLogs: boolean
	hasCompletionSummary: boolean
	logCount: number
} {
	const requestLogs =
		getLogsContaining("=== FIM COMPLETION REQUEST ===") || getLogsContaining("=== STANDARD API COMPLETION REQUEST ===")
	const responseLogs =
		getLogsContaining("=== FIM COMPLETION RESPONSE ===") || getLogsContaining("=== STANDARD API COMPLETION RESPONSE ===")
	const finalProcessingLogs = getLogsContaining("=== FINAL COMPLETION PROCESSING ===")
	const completionSummary = getLogsContaining("=== COMPLETION SUMMARY ===")

	return {
		hasRequestLogs: requestLogs.length > 0,
		hasResponseLogs: responseLogs.length > 0,
		hasFinalProcessingLogs: finalProcessingLogs.length > 0,
		hasCompletionSummary: completionSummary.length > 0,
		logCount: capturedLogs.length,
	}
}

// Example usage:
// startLogCapture()
// // Trigger autocomplete...
// const logs = stopLogCapture()
// const verification = verifyLoggingStructure()
// console.log("Logging verification:", verification)
