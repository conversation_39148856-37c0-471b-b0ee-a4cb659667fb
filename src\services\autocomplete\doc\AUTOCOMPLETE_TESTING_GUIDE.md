# QAX Autocomplete Testing Guide

This guide provides step-by-step instructions for testing the QAX Autocomplete integration in Cline.

## Prerequisites

1. Build the extension: `npm run compile`
2. Install dependencies: `npm install`
3. Have a valid API key for OpenRouter or compatible service

## Manual Testing Steps

### 1. Extension Loading Test

1. Open VS Code with the Cline extension
2. Check that the extension loads without errors
3. Verify that no console errors related to autocomplete appear

### 2. Configuration UI Test

1. Open Cline settings (click the settings gear icon)
2. Navigate to the "Autocomplete" tab
3. Verify the following fields are present:
   - Enable QAX Autocomplete (checkbox)
   - API Key (password field)
   - API Base URL (text field, default: https://api.openrouter.ai/api/v1)
   - Model ID (text field, default: google/gemini-2.5-flash-preview-05-20)
   - <PERSON>s (number field, default: 1000)
   - Temperature (number field, default: 0.1)
   - Request Timeout (number field, default: 30000)
   - Use Prompt Cache (checkbox)

### 3. Configuration Persistence Test

1. Configure autocomplete settings:
   - Enable QAX Autocomplete: ✓
   - API Key: [Your API Key]
   - Leave other settings as default
2. Click "Save Settings"
3. Close and reopen VS Code
4. Check that settings are preserved

### 4. Status Bar Test

1. With autocomplete disabled:
   - Look for "$(circle-slash) QAX Complete" in status bar
   - Tooltip should show "QAX Code Autocomplete (disabled)"

2. With autocomplete enabled but no API key:
   - Look for "$(warning) QAX Complete" in status bar
   - Tooltip should show "A valid API key must be set to use autocomplete"

3. With autocomplete enabled and API key set:
   - Look for "$(sparkle) QAX Complete" in status bar
   - Tooltip should show autocomplete information

### 5. Command Test

1. Open Command Palette (Ctrl+Shift+P / Cmd+Shift+P)
2. Search for "QAX" commands
3. Verify these commands exist:
   - "Toggle QAX Autocomplete"
   - "Track Accepted Suggestion"

### 6. Autocomplete Functionality Test

1. Enable autocomplete and set a valid API key
2. Open a code file (JavaScript, TypeScript, Python, etc.)
3. Start typing code
4. Verify that autocomplete suggestions appear (if API key is valid)
5. Test accepting suggestions

### 7. Error Handling Test

1. Set an invalid API key
2. Try to use autocomplete
3. Verify graceful error handling

### 8. Configuration Validation Test

1. Try to set invalid values:
   - Max Tokens: 0 or > 10000
   - Temperature: < 0 or > 2
   - Request Timeout: < 1000 or > 300000
2. Verify validation errors appear

## Expected Behavior

### Status Bar States

- **Disabled**: "$(circle-slash) QAX Complete"
- **No API Key**: "$(warning) QAX Complete"
- **Active**: "$(sparkle) QAX Complete ($0.00)"

### Configuration Persistence

- Settings should persist across VS Code restarts
- API key should be stored securely in VS Code secrets
- Other settings should be stored in VS Code configuration

### Error Handling

- Invalid configurations should show validation errors
- Network errors should be handled gracefully
- No crashes should occur from autocomplete functionality

## Troubleshooting

### Common Issues

1. **Autocomplete not appearing**:
   - Check that autocomplete is enabled
   - Verify API key is set and valid
   - Check network connectivity
   - Look for errors in VS Code Developer Console

2. **Settings not saving**:
   - Check for validation errors
   - Verify VS Code has write permissions
   - Try restarting VS Code

3. **Status bar not updating**:
   - Check that the extension is active
   - Try toggling autocomplete on/off
   - Restart VS Code

### Debug Information

- Check VS Code Developer Console for errors
- Look for autocomplete-related log messages
- Verify network requests in browser dev tools (if applicable)

## Test Checklist

- [ ] Extension loads without errors
- [ ] Autocomplete tab appears in settings
- [ ] All configuration fields are present
- [ ] Settings save and persist
- [ ] Status bar shows correct states
- [ ] Commands are registered
- [ ] Autocomplete functionality works (with valid API key)
- [ ] Error handling works correctly
- [ ] Configuration validation works
- [ ] No memory leaks or performance issues
