import { McpDisplayMode as ProtoMcpDisplayMode } from "@shared/proto/cline/state"
import { McpDisplayMode } from "../../McpDisplayMode"

/**
 * Converts a domain McpDisplayMode string to a proto McpDisplayMode enum
 */
export function convertDomainMcpDisplayModeToProto(mode: McpDisplayMode): ProtoMcpDisplayMode {
	switch (mode) {
		case "rich":
			return ProtoMcpDisplayMode.RICH
		case "plain":
			return ProtoMcpDisplayMode.PLAIN
		case "markdown":
			return ProtoMcpDisplayMode.MARKDOWN
		default:
			return ProtoMcpDisplayMode.PLAIN
	}
}

/**
 * Converts a proto McpDisplayMode enum to a domain McpDisplayMode string
 */
export function convertProtoMcpDisplayModeToDomain(mode: ProtoMcpDisplayMode): McpDisplayMode {
	switch (mode) {
		case ProtoMcpDisplayMode.RICH:
			return "rich"
		case ProtoMcpDisplayMode.PLAIN:
			return "plain"
		case ProtoMcpDisplayMode.MARKDOWN:
			return "markdown"
		default:
			return "plain"
	}
}
