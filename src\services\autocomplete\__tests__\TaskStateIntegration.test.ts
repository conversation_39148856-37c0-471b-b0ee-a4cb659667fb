import { expect } from "chai"
import { AutocompleteTaskManager, TaskExecutionState } from "../AutocompleteTaskManager"

describe("AutocompleteTaskManager - Task State Integration", () => {
	let taskManager: AutocompleteTaskManager
	let statusBarUpdateCallCount: number
	let mockStatusBarUpdate: () => void

	beforeEach(() => {
		// Reset singleton instance for each test
		;(AutocompleteTaskManager as any).instance = null
		taskManager = AutocompleteTaskManager.getInstance()

		// Set up mock status bar update callback
		statusBarUpdateCallCount = 0
		mockStatusBarUpdate = () => {
			statusBarUpdateCallCount++
		}
		taskManager.setStatusBarUpdateCallback(mockStatusBarUpdate)
	})

	afterEach(() => {
		// Clean up
		taskManager.forceReset()
	})

	describe("AI Response Generation Scenarios", () => {
		it("should disable autocomplete during AI say operations", () => {
			// Simulate AI starting to generate response
			taskManager.onTaskStart()

			expect(taskManager.getTaskState()).to.equal(TaskExecutionState.RUNNING)
			expect(taskManager.isTemporarilyDisabled()).to.equal(true)
			expect(taskManager.isWaitingForUser()).to.equal(false)
		})

		it("should handle streaming response completion correctly", () => {
			// Start with AI generating response
			taskManager.onTaskStart()
			expect(taskManager.getTaskState()).to.equal(TaskExecutionState.RUNNING)

			// AI completes response and waits for user interaction
			taskManager.onTaskWaitingForUser()
			expect(taskManager.getTaskState()).to.equal(TaskExecutionState.WAITING_FOR_USER)
			expect(taskManager.isTemporarilyDisabled()).to.equal(false)
			expect(taskManager.isWaitingForUser()).to.equal(true)
		})
	})

	describe("User Interaction Scenarios", () => {
		it("should enable autocomplete for completion_result ask", () => {
			// Task is running
			taskManager.onTaskStart()
			expect(taskManager.isTemporarilyDisabled()).to.equal(true)

			// Task completes and shows Start New Task button
			taskManager.onTaskWaitingForUser()
			expect(taskManager.isTemporarilyDisabled()).to.equal(false)
			expect(taskManager.isWaitingForUser()).to.equal(true)
		})
	})

	describe("Task Completion Scenarios", () => {
		it("should restore autocomplete when task completes successfully", () => {
			// Task is running
			taskManager.onTaskStart()
			expect(taskManager.getTaskState()).to.equal(TaskExecutionState.RUNNING)

			// User clicks Start New Task
			taskManager.onTaskStop()
			expect(taskManager.getTaskState()).to.equal(TaskExecutionState.IDLE)
			expect(taskManager.isTemporarilyDisabled()).to.equal(false)
			expect(taskManager.isWaitingForUser()).to.equal(false)
		})

		it("should restore autocomplete when task is cancelled", () => {
			// Task is running
			taskManager.onTaskStart()
			expect(taskManager.getTaskState()).to.equal(TaskExecutionState.RUNNING)

			// Task is cancelled/aborted
			taskManager.onTaskStop()
			expect(taskManager.getTaskState()).to.equal(TaskExecutionState.IDLE)
			expect(taskManager.isTemporarilyDisabled()).to.equal(false)
		})
	})

	describe("Complex Task Flow Scenarios", () => {
		it("should handle task completion with command execution", () => {
			// 1. Task starts
			taskManager.onTaskStart()
			expect(taskManager.getTaskState()).to.equal(TaskExecutionState.RUNNING)

			// 2. Task completes and shows command approval
			taskManager.onTaskWaitingForUser()
			expect(taskManager.getTaskState()).to.equal(TaskExecutionState.WAITING_FOR_USER)
			expect(taskManager.isTemporarilyDisabled()).to.equal(false)

			// 3. User approves command, task resumes execution
			taskManager.onTaskResumeFromWaiting()
			expect(taskManager.getTaskState()).to.equal(TaskExecutionState.RUNNING)
			expect(taskManager.isTemporarilyDisabled()).to.equal(true)

			// 4. Command completes, shows final completion
			taskManager.onTaskWaitingForUser()
			expect(taskManager.getTaskState()).to.equal(TaskExecutionState.WAITING_FOR_USER)
			expect(taskManager.isTemporarilyDisabled()).to.equal(false)

			// 5. User starts new task
			taskManager.onTaskStop()
			expect(taskManager.getTaskState()).to.equal(TaskExecutionState.IDLE)
		})
	})

	describe("Status Bar Integration", () => {
		it("should trigger status bar updates on state changes", () => {
			expect(statusBarUpdateCallCount).to.equal(0)

			taskManager.onTaskStart()
			expect(statusBarUpdateCallCount).to.equal(1)

			taskManager.onTaskWaitingForUser()
			expect(statusBarUpdateCallCount).to.equal(2)

			taskManager.onTaskResumeFromWaiting()
			expect(statusBarUpdateCallCount).to.equal(3)

			taskManager.onTaskStop()
			expect(statusBarUpdateCallCount).to.equal(4)
		})
	})
})
