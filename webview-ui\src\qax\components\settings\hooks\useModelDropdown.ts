import { useState, useRef, useEffect, useCallback, KeyboardEvent } from "react"

/**
 * 模型下拉框的自定义 Hook
 * 提供下拉框的状态管理、键盘导航和交互逻辑
 */
export const useModelDropdown = (availableModels: string[], currentModelId: string, onModelChange: (modelId: string) => void) => {
	const [searchTerm, setSearchTerm] = useState(currentModelId)
	const [isDropdownVisible, setIsDropdownVisible] = useState(false)
	const [selectedIndex, setSelectedIndex] = useState(-1)
	const dropdownRef = useRef<HTMLDivElement>(null)
	const itemRefs = useRef<(HTMLDivElement | null)[]>([])
	const dropdownListRef = useRef<HTMLDivElement>(null)

	// Handle model change
	const handleModelChange = useCallback(
		(newModelId: string) => {
			setSearchTerm(newModelId)
			onModelChange(newModelId)
			setIsDropdownVisible(false)
		},
		[onModelChange],
	)

	// Handle keyboard navigation
	const handleKeyDown = useCallback(
		(event: KeyboardEvent<HTMLInputElement>) => {
			if (!isDropdownVisible) {
				if (event.key === "Enter" || event.key === " ") {
					event.preventDefault()
					setIsDropdownVisible(true)
				}
				return
			}

			switch (event.key) {
				case "ArrowDown":
					event.preventDefault()
					setSelectedIndex((prev) => (prev < availableModels.length - 1 ? prev + 1 : prev))
					break
				case "ArrowUp":
					event.preventDefault()
					setSelectedIndex((prev) => (prev > 0 ? prev - 1 : prev))
					break
				case "Enter":
					event.preventDefault()
					if (selectedIndex >= 0 && selectedIndex < availableModels.length) {
						handleModelChange(availableModels[selectedIndex])
					}
					break
				case "Escape":
					setIsDropdownVisible(false)
					setSelectedIndex(-1)
					break
			}
		},
		[isDropdownVisible, availableModels, selectedIndex, handleModelChange],
	)

	// Sync external changes when the modelId changes
	useEffect(() => {
		setSearchTerm(currentModelId)
	}, [currentModelId])

	// Handle click outside to close dropdown
	useEffect(() => {
		const handleClickOutside = (event: MouseEvent) => {
			if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
				setIsDropdownVisible(false)
			}
		}

		document.addEventListener("mousedown", handleClickOutside)
		return () => {
			document.removeEventListener("mousedown", handleClickOutside)
		}
	}, [])

	// Reset selected index when dropdown opens
	useEffect(() => {
		if (isDropdownVisible) {
			setSelectedIndex(-1)
			if (dropdownListRef.current) {
				dropdownListRef.current.scrollTop = 0
			}
		}
	}, [isDropdownVisible])

	// Scroll to selected item
	useEffect(() => {
		if (selectedIndex >= 0 && itemRefs.current[selectedIndex]) {
			itemRefs.current[selectedIndex]?.scrollIntoView({
				block: "nearest",
				behavior: "smooth",
			})
		}
	}, [selectedIndex])

	return {
		searchTerm,
		isDropdownVisible,
		setIsDropdownVisible,
		selectedIndex,
		setSelectedIndex,
		dropdownRef,
		itemRefs,
		dropdownListRef,
		handleModelChange,
		handleKeyDown,
	}
}
