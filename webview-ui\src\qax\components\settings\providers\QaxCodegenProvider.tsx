import { openAiModelInfoSaneDefaults } from "@shared/api"
import { OpenAiModelsRequest } from "@shared/proto/cline/models"
import { QaxUtilsServiceClient } from "@/services/grpc-client"
import { getAsVar, VSC_DESCRIPTION_FOREGROUND } from "@/utils/vscStyles"
import { VSCodeCheckbox } from "@vscode/webview-ui-toolkit/react"
import { useCallback, useEffect, useState, useMemo } from "react"
import { useMount } from "react-use"
import { VSCodeTextField } from "@vscode/webview-ui-toolkit/react"
import { DebouncedTextField } from "@/components/settings/common/DebouncedTextField"
import { ModelInfoView } from "@/components/settings/common/ModelInfoView"
import { normalizeApiConfiguration, getModeSpecificFields } from "@/components/settings/utils/providerUtils"
import { useExtensionState } from "@/context/ExtensionStateContext"
import { useApiConfigurationHandlers } from "@/components/settings/utils/useApiConfigurationHandlers"
import { useClineAuth } from "@/context/ClineAuthContext"
import { QaxAccountInfoCard } from "../../account/QaxAccountInfoCard"
import { useModelDropdown } from "../hooks/useModelDropdown"
import { Mode } from "@shared/storage/types"

/**
 * QAX Codegen 提供器配置组件属性
 * <AUTHOR>
 */
interface QaxCodegenProviderProps {
	showModelOptions: boolean
	isPopup?: boolean
	currentMode: Mode
}

/**
 * QAX Codegen 提供器配置组件
 * 提供 QAX Codegen 服务的完整配置界面，包括账户信息、模型选择和参数配置
 * <AUTHOR>
 */
export const QaxCodegenProvider = ({ showModelOptions, isPopup, currentMode }: QaxCodegenProviderProps) => {
	const { handleModeFieldsChange, handleModeFieldChange } = useApiConfigurationHandlers()
	const { apiConfiguration } = useExtensionState()
	const { qaxUser } = useClineAuth()
	const modeFields = getModeSpecificFields(apiConfiguration, currentMode)

	const [modelConfigurationSelected, setModelConfigurationSelected] = useState(false)
	const [availableModels, setAvailableModels] = useState<string[]>([])
	const [isLoadingModels, setIsLoadingModels] = useState(false)

	// Get qaxCodegenModelInfo from mode fields
	const { qaxCodegenModelInfo } = modeFields

	// Use model dropdown hook
	const {
		searchTerm,
		isDropdownVisible,
		setIsDropdownVisible,
		selectedIndex,
		setSelectedIndex,
		dropdownRef,
		itemRefs,
		dropdownListRef,
		handleModelChange: onModelChange,
		handleKeyDown,
	} = useModelDropdown(availableModels, modeFields.qaxCodegenModelId || "", (newModelId: string) => {
		handleModeFieldsChange(
			{
				qaxCodegenModelId: { plan: "planModeQaxCodegenModelId", act: "actModeQaxCodegenModelId" },
				qaxCodegenModelInfo: { plan: "planModeQaxCodegenModelInfo", act: "actModeQaxCodegenModelInfo" },
			},
			{
				qaxCodegenModelId: newModelId,
				// Keep current mode's model info when switching models, like other providers
				qaxCodegenModelInfo: qaxCodegenModelInfo || openAiModelInfoSaneDefaults,
			},
			currentMode,
		)
	})

	// Get normalized configuration for ModelInfoView
	const { selectedModelInfo } = useMemo(() => {
		return normalizeApiConfiguration(apiConfiguration, currentMode)
	}, [apiConfiguration, currentMode])

	// Fetch models - like OpenRouter's refreshOpenRouterModels
	const refreshQaxCodegenModels = useCallback(async () => {
		setIsLoadingModels(true)
		try {
			const response = await QaxUtilsServiceClient.getQaxCodegenModels(OpenAiModelsRequest.create({}))
			const models = response.values || []
			setAvailableModels(models)
		} catch (error) {
			console.error("[QaxCodegenProvider] Failed to fetch QAX Codegen models:", error)
			// Set default models as fallback
			const defaultModels = ["DeepSeek-V3", "DeepSeek-R1"]
			setAvailableModels(defaultModels)
		} finally {
			setIsLoadingModels(false)
		}
	}, [])

	useMount(refreshQaxCodegenModels)

	// Reset model configuration expanded state when mode changes
	useEffect(() => {
		setModelConfigurationSelected(false)
	}, [currentMode])

	return (
		<div>
			<QaxAccountInfoCard />

			{/* Model Selection */}
			<div style={{ marginTop: 15 }}>
				<label htmlFor="qax-codegen-model">
					<span style={{ fontWeight: 500 }}>Model</span>
				</label>
				<div ref={dropdownRef} style={{ position: "relative", width: "100%" }}>
					<VSCodeTextField
						id="qax-codegen-model"
						value={searchTerm}
						onFocus={() => setIsDropdownVisible(true)}
						onKeyDown={handleKeyDown}
						style={{ width: "100%", marginTop: "4px", position: "relative", zIndex: 1000 }}
						disabled={!qaxUser}
						readOnly={true}
						placeholder={
							!qaxUser ? "请先登录 QAX 账户" : isLoadingModels ? "Loading models..." : "Click to select a model..."
						}>
						<div
							className="input-icon-button codicon codicon-chevron-down"
							aria-label="Open dropdown"
							onClick={() => setIsDropdownVisible(!isDropdownVisible)}
							slot="end"
							style={{
								display: "flex",
								justifyContent: "center",
								alignItems: "center",
								height: "100%",
								cursor: "pointer",
							}}
						/>
					</VSCodeTextField>
					{isDropdownVisible && qaxUser && availableModels.length > 0 && (
						<div
							ref={dropdownListRef}
							style={{
								position: "absolute",
								top: "calc(100% - 3px)",
								left: 0,
								width: "calc(100% - 2px)",
								maxHeight: "200px",
								overflowY: "auto",
								backgroundColor: "var(--vscode-dropdown-background)",
								border: "1px solid var(--vscode-list-activeSelectionBackground)",
								borderBottomLeftRadius: "3px",
								borderBottomRightRadius: "3px",
								zIndex: 999,
							}}>
							{availableModels.length > 0 ? (
								availableModels.map((model, index) => {
									const isSelected = model === modeFields.qaxCodegenModelId
									const isHighlighted = index === selectedIndex
									return (
										<div
											key={model}
											ref={(el) => (itemRefs.current[index] = el)}
											style={{
												padding: "5px 10px",
												cursor: "pointer",
												wordBreak: "break-all",
												whiteSpace: "normal",
												backgroundColor: isHighlighted
													? "var(--vscode-list-activeSelectionBackground)"
													: isSelected
														? "var(--vscode-list-inactiveSelectionBackground)"
														: "inherit",
												color: isSelected ? "var(--vscode-list-activeSelectionForeground)" : "inherit",
												fontWeight: isSelected ? "bold" : "normal",
											}}
											onMouseEnter={() => setSelectedIndex(index)}
											onClick={() => {
												onModelChange(model)
											}}>
											{model}
										</div>
									)
								})
							) : (
								<div
									style={{
										padding: "10px",
										textAlign: "center",
										color: "var(--vscode-descriptionForeground)",
										fontStyle: "italic",
									}}>
									{isLoadingModels ? "Loading models..." : "No models available"}
								</div>
							)}
						</div>
					)}
				</div>
			</div>

			{!qaxUser && (
				<div
					style={{
						padding: "8px 12px",
						backgroundColor: "var(--vscode-inputValidation-warningBackground)",
						border: "1px solid var(--vscode-inputValidation-warningBorder)",
						borderRadius: "3px",
						marginBottom: 10,
					}}>
					<p style={{ margin: 0, fontSize: "12px", color: "var(--vscode-inputValidation-warningForeground)" }}>
						⚠️ 未登录 QAX 账户，模型选择已禁用。请先登录以获取可用模型。
					</p>
				</div>
			)}

			<div
				style={{
					color: getAsVar(VSC_DESCRIPTION_FOREGROUND),
					display: "flex",
					margin: "15px 0 10px 0",
					cursor: "pointer",
					alignItems: "center",
				}}
				onClick={() => setModelConfigurationSelected((val) => !val)}>
				<span
					className={`codicon ${modelConfigurationSelected ? "codicon-chevron-down" : "codicon-chevron-right"}`}
					style={{ marginRight: "4px" }}
				/>
				<span style={{ fontWeight: 500, textTransform: "uppercase" }}>模型配置</span>
			</div>

			{modelConfigurationSelected && (
				<>
					<VSCodeCheckbox
						checked={!!qaxCodegenModelInfo?.supportsImages}
						onChange={(e: any) => {
							const isChecked = e.target.checked === true
							const modelInfo = qaxCodegenModelInfo ? qaxCodegenModelInfo : { ...openAiModelInfoSaneDefaults }
							modelInfo.supportsImages = isChecked
							handleModeFieldChange(
								{ plan: "planModeQaxCodegenModelInfo", act: "actModeQaxCodegenModelInfo" },
								modelInfo,
								currentMode,
							)
						}}>
						支持图像
					</VSCodeCheckbox>

					<VSCodeCheckbox
						checked={!!qaxCodegenModelInfo?.isR1FormatRequired}
						onChange={(e: any) => {
							const isChecked = e.target.checked === true
							let modelInfo = qaxCodegenModelInfo ? qaxCodegenModelInfo : { ...openAiModelInfoSaneDefaults }
							modelInfo = { ...modelInfo, isR1FormatRequired: isChecked }
							handleModeFieldChange(
								{ plan: "planModeQaxCodegenModelInfo", act: "actModeQaxCodegenModelInfo" },
								modelInfo,
								currentMode,
							)
						}}>
						启用 R1 消息格式
					</VSCodeCheckbox>

					<div style={{ display: "flex", gap: 10, marginTop: "5px" }}>
						<DebouncedTextField
							initialValue={
								qaxCodegenModelInfo?.contextWindow
									? qaxCodegenModelInfo.contextWindow.toString()
									: (openAiModelInfoSaneDefaults.contextWindow?.toString() ?? "")
							}
							style={{ flex: 1 }}
							onChange={(value) => {
								const modelInfo = qaxCodegenModelInfo ? qaxCodegenModelInfo : { ...openAiModelInfoSaneDefaults }
								modelInfo.contextWindow = Number(value)
								handleModeFieldChange(
									{ plan: "planModeQaxCodegenModelInfo", act: "actModeQaxCodegenModelInfo" },
									modelInfo,
									currentMode,
								)
							}}>
							<span style={{ fontWeight: 500 }}>上下文窗口大小</span>
						</DebouncedTextField>

						<DebouncedTextField
							initialValue={
								qaxCodegenModelInfo?.maxTokens
									? qaxCodegenModelInfo.maxTokens.toString()
									: (openAiModelInfoSaneDefaults.maxTokens?.toString() ?? "")
							}
							style={{ flex: 1 }}
							onChange={(value) => {
								const modelInfo = qaxCodegenModelInfo ? qaxCodegenModelInfo : { ...openAiModelInfoSaneDefaults }
								modelInfo.maxTokens = Number(value)
								handleModeFieldChange(
									{ plan: "planModeQaxCodegenModelInfo", act: "actModeQaxCodegenModelInfo" },
									modelInfo,
									currentMode,
								)
							}}>
							<span style={{ fontWeight: 500 }}>最大输出令牌数</span>
						</DebouncedTextField>
					</div>

					<div style={{ display: "flex", gap: 10, marginTop: "5px" }}>
						<DebouncedTextField
							initialValue={
								qaxCodegenModelInfo?.inputPrice
									? qaxCodegenModelInfo.inputPrice.toString()
									: (openAiModelInfoSaneDefaults.inputPrice?.toString() ?? "")
							}
							style={{ flex: 1 }}
							onChange={(value) => {
								const modelInfo = qaxCodegenModelInfo ? qaxCodegenModelInfo : { ...openAiModelInfoSaneDefaults }
								modelInfo.inputPrice = Number(value)
								handleModeFieldChange(
									{ plan: "planModeQaxCodegenModelInfo", act: "actModeQaxCodegenModelInfo" },
									modelInfo,
									currentMode,
								)
							}}>
							<span style={{ fontWeight: 500 }}>输入价格 / 100万令牌</span>
						</DebouncedTextField>

						<DebouncedTextField
							initialValue={
								qaxCodegenModelInfo?.outputPrice
									? qaxCodegenModelInfo.outputPrice.toString()
									: (openAiModelInfoSaneDefaults.outputPrice?.toString() ?? "")
							}
							style={{ flex: 1 }}
							onChange={(value) => {
								const modelInfo = qaxCodegenModelInfo ? qaxCodegenModelInfo : { ...openAiModelInfoSaneDefaults }
								modelInfo.outputPrice = Number(value)
								handleModeFieldChange(
									{ plan: "planModeQaxCodegenModelInfo", act: "actModeQaxCodegenModelInfo" },
									modelInfo,
									currentMode,
								)
							}}>
							<span style={{ fontWeight: 500 }}>输出价格 / 100万令牌</span>
						</DebouncedTextField>
					</div>

					<div style={{ display: "flex", gap: 10, marginTop: "5px" }}>
						<DebouncedTextField
							initialValue={
								qaxCodegenModelInfo?.temperature
									? qaxCodegenModelInfo.temperature.toString()
									: (openAiModelInfoSaneDefaults.temperature?.toString() ?? "")
							}
							onChange={(value) => {
								const modelInfo = qaxCodegenModelInfo ? qaxCodegenModelInfo : { ...openAiModelInfoSaneDefaults }

								const shouldPreserveFormat = value.endsWith(".") || (value.includes(".") && value.endsWith("0"))

								modelInfo.temperature =
									value === ""
										? openAiModelInfoSaneDefaults.temperature
										: shouldPreserveFormat
											? (value as any)
											: parseFloat(value)

								handleModeFieldChange(
									{ plan: "planModeQaxCodegenModelInfo", act: "actModeQaxCodegenModelInfo" },
									modelInfo,
									currentMode,
								)
							}}>
							<span style={{ fontWeight: 500 }}>Temperature</span>
						</DebouncedTextField>
					</div>
				</>
			)}

			<p
				style={{
					fontSize: "12px",
					marginTop: 10,
					color: "var(--vscode-descriptionForeground)",
				}}>
				QAX Codegen 使用 JWT 认证，请确保已登录 QAX 账户。
			</p>

			{showModelOptions && <ModelInfoView selectedModelId={searchTerm} modelInfo={selectedModelInfo} isPopup={isPopup} />}
		</div>
	)
}
